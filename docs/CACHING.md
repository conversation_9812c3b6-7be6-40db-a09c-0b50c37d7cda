# Request Caching System

The Cursor Stats extension implements a comprehensive request caching system to improve performance and reduce API calls. This document explains how the caching system works and how to use it.

## Overview

The caching system provides:
- **Generic caching** for any JSON-serializable data
- **File-based storage** using the extension directory
- **TTL (Time-To-Live) support** with automatic expiration
- **Cache key generation** for consistent caching
- **Automatic cleanup** of expired entries
- **Error handling** with graceful fallbacks

## Architecture

### Components

1. **CacheService** (`src/services/cache.ts`) - Main cache implementation
2. **ICacheService** (`src/services/interfaces.ts`) - Cache service interface
3. **Cache Types** (`src/interfaces/types.ts`) - Type definitions
4. **Cache Constants** (`src/constants/index.ts`) - Configuration values

### Cache Storage

- **Location**: Extension directory (`api-request-cache.json`)
- **Format**: JSON file with cache entries
- **Structure**: Each entry contains data, timestamp, TTL, and key

## Usage

### Basic Caching

```typescript
import { getServices } from '../services/registry';
import { TIME } from '../constants';

const services = getServices();

// Generate cache key
const cacheKey = services.cacheService.generateKey('endpoint', { userId: '123' });

// Try to get cached data
const cachedData = await services.cacheService.get(cacheKey);

if (cachedData) {
  return cachedData; // Use cached data
}

// Make API call if cache miss
const apiData = await makeApiCall();

// Cache the result
await services.cacheService.set(cacheKey, apiData, {
  ttl: TIME.CACHE_DURATIONS.CURSOR_STATS, // 5 minutes
});

return apiData;
```

### Cache Key Generation

The cache service provides a `generateKey` method that creates consistent, unique keys:

```typescript
// Simple key
const key1 = services.cacheService.generateKey('user-profile');

// User-specific key
const key2 = services.cacheService.generateKey('user-stats', {}, 'user123');

// Parameterized key
const key3 = services.cacheService.generateKey('team-data', {
  teamId: 456,
  includeMembers: true,
});
```

### Cache Management

```typescript
// Check if entry exists
const exists = await services.cacheService.has('cache-key');

// Delete specific entry
await services.cacheService.delete('cache-key');

// Clean up expired entries
await services.cacheService.cleanup();

// Clear all cache (use with caution)
await services.cacheService.clear();
```

## Cache Durations

The system defines different TTL values for different types of data:

| Data Type | Duration | Constant |
|-----------|----------|----------|
| Cursor Stats | 5 minutes | `TIME.CACHE_DURATIONS.CURSOR_STATS` |
| Usage-Based Status | 15 minutes | `TIME.CACHE_DURATIONS.USAGE_BASED_STATUS` |
| Usage Limits | 10 minutes | `TIME.CACHE_DURATIONS.USAGE_LIMIT` |
| Monthly Data | 30 minutes | `TIME.CACHE_DURATIONS.MONTHLY_DATA` |
| Team Membership | 1 hour | `TIME.CACHE_DURATIONS.TEAM_MEMBERSHIP` |

## API Integration

The caching system is integrated into the main API functions:

### fetchCursorStats()
- **Cache Key**: Based on user ID
- **TTL**: 5 minutes
- **Benefits**: Reduces frequent stats API calls

### checkUsageBasedStatus()
- **Cache Key**: Based on user ID and team ID
- **TTL**: 15 minutes
- **Benefits**: Reduces pricing status checks

### getCurrentUsageLimit()
- **Cache Key**: Based on user ID and team ID
- **TTL**: 10 minutes
- **Benefits**: Reduces limit API calls

### fetchMonthData()
- **Cache Key**: Based on user ID, month, and year
- **TTL**: 30 minutes
- **Benefits**: Reduces monthly invoice API calls

## Error Handling

The caching system includes robust error handling:

```typescript
try {
  const cachedData = await services.cacheService.get(cacheKey);
  if (cachedData) {
    return cachedData;
  }
} catch (cacheError) {
  // Log error but continue with API call
  services.loggingService.log('Cache read error', cacheError.message);
}

// Make API call
const apiData = await makeApiCall();

try {
  await services.cacheService.set(cacheKey, apiData, { ttl: 300000 });
} catch (cacheError) {
  // Log error but don't fail the operation
  services.loggingService.log('Cache write error', cacheError.message);
}

return apiData;
```

## Cache Cleanup

The system automatically cleans up expired entries:

1. **On Extension Startup** - Removes expired entries during initialization
2. **On Extension Deactivation** - Cleans up before shutdown
3. **Manual Cleanup** - Can be triggered programmatically

## Testing

The caching system includes comprehensive tests in `src/test/cache.test.ts`:

- Basic cache operations (get, set, delete)
- TTL expiration handling
- Cache key generation
- Cleanup functionality
- Error scenarios

Run tests with:
```bash
npm test
```

## Performance Benefits

The caching system provides significant performance improvements:

- **Reduced API Calls**: Avoids redundant requests within TTL window
- **Faster Response Times**: Cached data returns immediately
- **Better User Experience**: Reduces loading delays
- **API Rate Limiting**: Helps stay within API limits

## Configuration

Cache behavior can be customized by modifying constants in `src/constants/index.ts`:

```typescript
export const TIME = {
  CACHE_DURATIONS: {
    CURSOR_STATS: 5 * 60 * 1000,        // 5 minutes
    USAGE_BASED_STATUS: 15 * 60 * 1000, // 15 minutes
    USAGE_LIMIT: 10 * 60 * 1000,        // 10 minutes
    MONTHLY_DATA: 30 * 60 * 1000,       // 30 minutes
    TEAM_MEMBERSHIP: 60 * 60 * 1000,    // 1 hour
  },
};
```

## Best Practices

1. **Use appropriate TTL values** - Balance freshness vs. performance
2. **Handle cache errors gracefully** - Don't let cache failures break functionality
3. **Generate consistent keys** - Use the provided `generateKey` method
4. **Clean up regularly** - The system handles this automatically
5. **Monitor cache performance** - Check logs for cache hit/miss ratios

## Troubleshooting

### Common Issues

1. **Cache not working**: Check file permissions in extension directory
2. **Stale data**: Verify TTL values are appropriate
3. **High memory usage**: Ensure cleanup is running properly
4. **Key collisions**: Use unique parameters in key generation

### Debugging

Enable detailed logging to debug cache issues:

```typescript
services.loggingService.log('[Cache] Debug info', {
  key: cacheKey,
  exists: await services.cacheService.has(cacheKey),
  // Add other debug info
});
```

## Future Enhancements

Potential improvements to the caching system:

- **Memory-based caching** for frequently accessed data
- **Cache size limits** with LRU eviction
- **Cache statistics** and monitoring
- **Distributed caching** for multi-instance scenarios
- **Cache warming** strategies
